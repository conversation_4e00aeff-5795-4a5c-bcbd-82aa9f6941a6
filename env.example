# ==============================================
# VUR - Time Series Forecasting Platform
# Arquivo de Configuração de Ambiente
# ==============================================

# ==============================================
# BANCO DE DADOS - PostgreSQL
# ==============================================
POSTGRES_DB=vur_db
POSTGRES_USER=vur_user
POSTGRES_PASSWORD=vur_password_change_in_production
POSTGRES_PORT=5432

# ==============================================
# BACKEND - FastAPI
# ==============================================
BACKEND_PORT=8000

# JWT Configuration
SECRET_KEY=your-super-secret-jwt-key-change-in-production-minimum-32-characters
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR=./uploads

# ML Models Configuration
MODELS_DIR=./models

# ==============================================
# FRONTEND - React/Vite
# ==============================================
FRONTEND_PORT=3000

# API Configuration
VITE_API_URL=http://localhost:8000
VITE_APP_NAME=VUR - Time Series Forecasting
VITE_ENVIRONMENT=development

# ==============================================
# REDIS - Cache (Opcional)
# ==============================================
REDIS_PORT=6379
REDIS_PASSWORD=redis_password_change_in_production

# ==============================================
# NGINX - Proxy Reverso (Produção)
# ==============================================
NGINX_PORT=80
NGINX_SSL_PORT=443

# ==============================================
# CONFIGURAÇÕES DE PRODUÇÃO
# ==============================================
# Descomente e configure para produção

# SSL Configuration
# SSL_CERT_PATH=/path/to/ssl/cert.pem
# SSL_KEY_PATH=/path/to/ssl/private.key

# Domain Configuration
# DOMAIN=yourdomain.com
# SUBDOMAIN=api.yourdomain.com

# Email Configuration (para notificações)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_FROM=<EMAIL>

# Monitoring (opcional)
# SENTRY_DSN=your-sentry-dsn
# PROMETHEUS_PORT=9090

# ==============================================
# CONFIGURAÇÕES DE DESENVOLVIMENTO
# ==============================================

# Hot Reload
HOT_RELOAD=true

# Debug SQL Queries
DEBUG_SQL=false

# Mock Data
USE_MOCK_DATA=false

# ==============================================
# CONFIGURAÇÕES DE MACHINE LEARNING
# ==============================================

# Model Training
MAX_TRAINING_TIME=3600  # 1 hour in seconds
DEFAULT_TRAIN_TEST_SPLIT=0.8
DEFAULT_VALIDATION_SPLIT=0.2

# Prediction Settings
MAX_PREDICTION_HORIZON=365  # days
DEFAULT_CONFIDENCE_INTERVAL=0.95

# Model Storage
MODEL_STORAGE_TYPE=local  # local, s3, gcs
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_BUCKET_NAME=your-s3-bucket

# ==============================================
# CONFIGURAÇÕES DE SEGURANÇA
# ==============================================

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Session Configuration
SESSION_TIMEOUT=3600  # 1 hour in seconds

# Password Policy
MIN_PASSWORD_LENGTH=8
REQUIRE_SPECIAL_CHARS=true
REQUIRE_NUMBERS=true
REQUIRE_UPPERCASE=true

# ==============================================
# CONFIGURAÇÕES DE BACKUP
# ==============================================

# Database Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# File Backup
BACKUP_UPLOADS=true
BACKUP_MODELS=true

# ==============================================
# NOTAS IMPORTANTES
# ==============================================
# 1. Copie este arquivo para .env e configure os valores
# 2. NUNCA commite o arquivo .env no repositório
# 3. Use senhas fortes em produção
# 4. Configure SSL/TLS em produção
# 5. Configure backup automático em produção
# 6. Use variáveis de ambiente do sistema em produção 