[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "vur-backend"
version = "1.0.0"
description = "VUR - Time Series Forecasting Platform Backend"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "VUR Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "VUR Team", email = "<EMAIL>"}
]
keywords = [
    "time-series",
    "forecasting",
    "machine-learning",
    "fastapi",
    "python"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    "psycopg2-binary>=2.9.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.0",
    "python-multipart>=0.0.6",
    "python-dotenv>=1.0.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "pandas>=2.1.0",
    "numpy>=1.25.0",
    "scikit-learn>=1.3.0",
    "statsmodels>=0.14.0",
    "redis>=5.0.0",
    "structlog>=23.2.0",
    "aiofiles>=23.2.0",
    "python-dateutil>=2.8.0",
    "pytz>=2023.3",
]

[project.optional-dependencies]
ml = [
    "prophet>=1.1.0",
    "tensorflow>=2.15.0",
    "torch>=2.1.0",
    "xgboost>=2.0.0",
]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.0",
    "debugpy>=1.8.0",
    "ipython>=8.17.0",
]
monitoring = [
    "sentry-sdk[fastapi]>=1.38.0",
    "prometheus-client>=0.19.0",
    "psutil>=5.9.0",
]
docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
]
cloud = [
    "boto3>=1.34.0",
    "google-cloud-storage>=2.10.0",
]

[project.urls]
Homepage = "https://github.com/your-org/vur"
Documentation = "https://vur.readthedocs.io"
Repository = "https://github.com/your-org/vur.git"
"Bug Tracker" = "https://github.com/your-org/vur/issues"

[project.scripts]
vur-server = "main:main"
vur-migrate = "scripts.migrate:main"
vur-seed = "scripts.seed:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["app*", "scripts*"]
exclude = ["tests*"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app", "scripts"]
known_third_party = ["fastapi", "sqlalchemy", "pydantic"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    "migrations",
]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "prophet.*",
    "tensorflow.*",
    "torch.*",
    "xgboost.*",
    "statsmodels.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "ml: marks tests as machine learning tests",
]

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/venv/*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "migrations"]
skips = ["B101", "B601"] 