# Dockerfile para desenvolvimento do frontend com hot reload
FROM node:18-alpine

# Instalar dependências do sistema
RUN apk add --no-cache curl

# Configurar diretório de trabalho
WORKDIR /app

# Copiar arquivos de dependências
COPY package*.json ./

# Instalar dependências
RUN npm install

# Expor porta
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000 || exit 1

# Comando para desenvolvimento com hot reload
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"] 