# ===================================
# VUR Backend - Python Dependencies
# ===================================

# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Environment & Configuration
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP Client
httpx==0.25.2
requests==2.31.0

# Data Processing
pandas==2.1.4
numpy==1.25.2
scikit-learn==1.3.2

# Time Series & ML
statsmodels==0.14.0
prophet==1.1.4
tensorflow==2.15.0
torch==2.1.1
xgboost==2.0.2

# Data Validation
pydantic==2.5.0
email-validator==2.1.0

# File Processing
openpyxl==3.1.2
xlrd==2.0.1

# Caching (Redis)
redis==5.0.1
hiredis==2.2.3

# Background Tasks
celery==5.3.4
flower==2.0.1

# Monitoring & Logging
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# Development Tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Documentation
mkdocs==1.5.3
mkdocs-material==9.4.8

# Utilities
python-slugify==8.0.1
click==8.1.7
rich==13.7.0
typer==0.9.0

# Date & Time
python-dateutil==2.8.2
pytz==2023.3

# Image Processing (for charts/plots)
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# API Documentation
fastapi-users==12.1.2
fastapi-pagination==0.12.13

# File Upload & Storage
aiofiles==23.2.1
boto3==1.34.0  # For AWS S3 integration

# Validation & Serialization
marshmallow==3.20.1
cerberus==1.3.5

# Performance
asyncpg==0.29.0  # Async PostgreSQL driver
aiocache==0.12.2

# Security
cryptography==41.0.8
bcrypt==4.1.2

# Configuration Management
dynaconf==3.2.4

# Health Checks
healthcheck==1.3.3

# Rate Limiting
slowapi==0.1.9

# CORS
fastapi-cors==0.0.6

# WebSocket Support
websockets==12.0

# Job Queue
rq==1.15.1

# Metrics & Monitoring
prometheus-client==0.19.0
psutil==5.9.6

# Development & Debug
debugpy==1.8.0
ipython==8.17.2

# Data Export
xlsxwriter==3.1.9

# Timezone Support
zoneinfo==0.2.1; python_version < "3.9" 