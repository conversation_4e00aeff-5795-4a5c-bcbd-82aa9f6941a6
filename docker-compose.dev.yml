version: '3.8'

services:
  # Banco de dados PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: vur_postgres_dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-vur_db_dev}
      POSTGRES_USER: ${POSTGRES_USER:-vur_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-vur_password}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    networks:
      - vur_dev_network

  # Backend FastAPI (Development)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: vur_backend_dev
    restart: unless-stopped
    environment:
      # Database
      DATABASE_URL: postgresql://${POSTGRES_USER:-vur_user}:${POSTGRES_PASSWORD:-vur_password}@postgres:5432/${POSTGRES_DB:-vur_db_dev}
      
      # JWT
      SECRET_KEY: ${SECRET_KEY:-dev-secret-key-not-for-production}
      ALGORITHM: ${ALGORITHM:-HS256}
      ACCESS_TOKEN_EXPIRE_MINUTES: ${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      
      # Environment
      ENVIRONMENT: development
      DEBUG: true
      HOT_RELOAD: true
      
      # CORS
      ALLOWED_ORIGINS: http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000
      
      # File uploads
      MAX_FILE_SIZE: ${MAX_FILE_SIZE:-10485760}
      UPLOAD_DIR: /app/uploads
      
      # ML Models
      MODELS_DIR: /app/models
      
      # Logging
      LOG_LEVEL: DEBUG
    volumes:
      - ./backend:/app:cached  # Hot reload
      - backend_dev_uploads:/app/uploads
      - backend_dev_models:/app/models
      - backend_dev_logs:/app/logs
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    networks:
      - vur_dev_network
    depends_on:
      - postgres
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--log-level", "debug"]

  # Frontend React (Development)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: vur_frontend_dev
    restart: unless-stopped
    environment:
      - VITE_API_URL=http://localhost:8000
      - VITE_APP_NAME=VUR - Development
      - VITE_ENVIRONMENT=development
      - CHOKIDAR_USEPOLLING=true  # For hot reload in Docker
    volumes:
      - ./frontend:/app:cached  # Hot reload
      - /app/node_modules  # Anonymous volume for node_modules
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    networks:
      - vur_dev_network
    depends_on:
      - backend
    command: ["npm", "run", "dev", "--", "--host", "0.0.0.0"]

  # Redis para cache (Development)
  redis:
    image: redis:7-alpine
    container_name: vur_redis_dev
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - vur_dev_network

  # Adminer - Database Management
  adminer:
    image: adminer:latest
    container_name: vur_adminer_dev
    restart: unless-stopped
    ports:
      - "8080:8080"
    networks:
      - vur_dev_network
    depends_on:
      - postgres

  # Redis Commander - Redis Management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: vur_redis_commander_dev
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    networks:
      - vur_dev_network
    depends_on:
      - redis

  # Mailhog - Email Testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: vur_mailhog_dev
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - vur_dev_network

# Volumes para desenvolvimento
volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  backend_dev_uploads:
    driver: local
  backend_dev_models:
    driver: local
  backend_dev_logs:
    driver: local

# Rede para desenvolvimento
networks:
  vur_dev_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16 