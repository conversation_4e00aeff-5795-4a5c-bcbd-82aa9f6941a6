# =======================
# DEPENDÊNCIAS
# =======================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
.pnpm-debug.log*

# =======================
# BUILD E DISTRIBUIÇÃO
# =======================
dist/
dist-ssr/
build/
.next/
out/
*.tsbuildinfo

# =======================
# CACHE
# =======================
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# =======================
# VARIÁVEIS DE AMBIENTE
# =======================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.staging

# =======================
# TESTING
# =======================
coverage/
.nyc_output/
.coverage
junit.xml

# =======================
# LOGS
# =======================
logs/
*.log

# =======================
# RUNTIME DATA
# =======================
pids/
*.pid
*.seed
*.pid.lock

# =======================
# EDITOR E IDE
# =======================
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
.idea/
*.swp
*.swo
*~

# JetBrains
*.iws
*.iml
*.ipr

# =======================
# SISTEMA OPERACIONAL
# =======================
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*

# Linux
*~

# =======================
# TEMPORÁRIOS
# =======================
*.tmp
*.temp
*.local

# =======================
# FERRAMENTAS DE BUILD
# =======================
# Vite
.vite/

# Webpack
.webpack/

# Rollup
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# =======================
# STORYBOOK
# =======================
storybook-static/

# =======================
# OUTROS
# =======================
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Backup files
*.bak
*.backup
*.old
