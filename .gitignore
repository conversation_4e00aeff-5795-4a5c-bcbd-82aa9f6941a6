# =======================
# GERAL
# =======================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.tmp
*.temp
*~

# =======================
# FRONTEND (Node.js/React)
# =======================
# Dependencies
frontend/node_modules/
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*
frontend/pnpm-debug.log*
frontend/lerna-debug.log*

# Build outputs
frontend/dist/
frontend/dist-ssr/
frontend/build/
frontend/.next/
frontend/out/

# Environment variables
frontend/.env
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local

# Testing
frontend/coverage/
frontend/.nyc_output/

# Cache
frontend/.cache/
frontend/.parcel-cache/

# =======================
# BACKEND (Python)
# =======================
# Byte-compiled / optimized / DLL files
backend/__pycache__/
backend/*.py[cod]
backend/*$py.class

# C extensions
backend/*.so

# Distribution / packaging
backend/.Python
backend/build/
backend/develop-eggs/
backend/dist/
backend/downloads/
backend/eggs/
backend/.eggs/
backend/lib/
backend/lib64/
backend/parts/
backend/sdist/
backend/var/
backend/wheels/
backend/share/python-wheels/
backend/*.egg-info/
backend/.installed.cfg
backend/*.egg
backend/MANIFEST

# PyInstaller
backend/*.manifest
backend/*.spec

# Installer logs
backend/pip-log.txt
backend/pip-delete-this-directory.txt

# Unit test / coverage reports
backend/htmlcov/
backend/.tox/
backend/.nox/
backend/.coverage
backend/.coverage.*
backend/.cache
backend/nosetests.xml
backend/coverage.xml
backend/*.cover
backend/*.py,cover
backend/.hypothesis/
backend/.pytest_cache/
backend/cover/

# Translations
backend/*.mo
backend/*.pot

# Django stuff:
backend/*.log
backend/local_settings.py
backend/db.sqlite3
backend/db.sqlite3-journal

# Flask stuff:
backend/instance/
backend/.webassets-cache

# Scrapy stuff:
backend/.scrapy

# Sphinx documentation
backend/docs/_build/

# PyBuilder
backend/.pybuilder/
backend/target/

# Jupyter Notebook
backend/.ipynb_checkpoints

# IPython
backend/profile_default/
backend/ipython_config.py

# pyenv
backend/.python-version

# pipenv
backend/Pipfile.lock

# poetry
backend/poetry.lock

# pdm
backend/.pdm.toml
backend/.pdm-python
backend/.pdm-build/

# PEP 582
backend/__pypackages__/

# Celery stuff
backend/celerybeat-schedule
backend/celerybeat.pid

# SageMath parsed files
backend/*.sage.py

# Environments
backend/.env
backend/.venv
backend/env/
backend/venv/
backend/ENV/
backend/env.bak/
backend/venv.bak/

# Spyder project settings
backend/.spyderproject
backend/.spyproject

# Rope project settings
backend/.ropeproject

# mkdocs documentation
backend/site

# mypy
backend/.mypy_cache/
backend/.dmypy.json
backend/dmypy.json

# Pyre type checker
backend/.pyre/

# pytype static type analyzer
backend/.pytype/

# Cython debug symbols
backend/cython_debug/

# PyCharm
backend/.idea/

# =======================
# BANCO DE DADOS
# =======================
*.db
*.sqlite
*.sqlite3
database.db
db.sqlite3

# =======================
# LOGS
# =======================
logs/
*.log

# =======================
# VARIÁVEIS DE AMBIENTE
# =======================
.env
.env.local
.env.development
.env.production
.env.staging

# =======================
# EDITORES / IDEs
# =======================
.vscode/
.idea/
*.swp
*.swo
*~

# JetBrains
.idea/
*.iws
*.iml
*.ipr

# =======================
# SISTEMA OPERACIONAL
# =======================
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*

# Linux
*~

# =======================
# OUTROS
# =======================
# Backup files
*.bak
*.backup
*.old

# Archive files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Docker
.dockerignore
docker-compose.override.yml 