"""
Pipeline model
"""

from sqlalchemy import <PERSON>umn, Integer, String, DateTime
from sqlalchemy.sql import func

from app.core.database import Base


class Pipeline(Base):
    """Pipeline model - placeholder for FASE 2."""
    __tablename__ = "pipelines"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(String)
    status = Column(String, default="created")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now()) 